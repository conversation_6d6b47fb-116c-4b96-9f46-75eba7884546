import React, { useState, useEffect } from 'react';
import {
  Table as AntTable,
  Button as AntButton,
  Input,
  Space,
  Tag,
  Avatar,
  Tooltip,
  message,
  Modal,
  Empty,
  Pagination,
  Spin,
  DatePicker,
  Form,
  Descriptions,
  Badge,
  Tabs,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  DollarCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  BankOutlined,
  UserOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  EditOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useFetchClient } from '@strapi/strapi/admin';
import {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  StatsGrid,
  StatsCard,
  Button,
  FilterGroup,
  FilterLabel,
  StyledTable,
} from '../components/shared';
import { DollarSign, Clock, CheckCircle, XCircle, TrendingUp, Download } from 'lucide-react';
import * as XLSX from 'xlsx';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { confirm } = Modal;

// Types
interface Withdrawal {
  id: number;
  amount: number;
  bankName: string;
  bankAccount: string;
  accountHolder: string;
  statusPaid: 'pending' | 'approved' | 'rejected';
  note?: string;
  adminNote?: string;
  user: {
    id: number;
    name: string;
    phone: string;
    email: string;
    avatar?: string;
    balance: number;
  };
  createdAt: string;
  updatedAt: string;
  processedAt?: string;
  processedBy?: {
    id: number;
    name: string;
  };
}

interface WithdrawalListResponse {
  data: Withdrawal[];
  meta: {
    pagination: {
      total: number;
      page: number;
      pageSize: number;
      pageCount: number;
    };
  };
}

interface Statistics {
  totalWithdrawals: number;
  pendingWithdrawals: number;
  approvedWithdrawals: number;
  rejectedWithdrawals: number;
  totalAmount: number;
  pendingAmount: number;
  approvedAmount: number;
  rejectedAmount: number;
}

const WithdrawalList: React.FC = () => {
  const { get, put } = useFetchClient();

  const [withdrawals, setWithdrawals] = useState<Withdrawal[]>([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<Statistics | null>(null);
  const [status, setStatus] = useState('pending'); // Default to pending
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  // Modal states
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<Withdrawal | null>(null);
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [adminNoteForm] = Form.useForm();
  const [actionLoading, setActionLoading] = useState(false);

  const PAGE_SIZE = 10;

  // Stats data for display
  const statsData = [
    {
      title: 'Tổng yêu cầu',
      value: statistics?.totalWithdrawals || 0,
      icon: DollarSign,
      color: '#3b82f6',
    },
    {
      title: 'Chờ duyệt',
      value: statistics?.pendingWithdrawals || 0,
      icon: Clock,
      color: '#f59e0b',
    },
    {
      title: 'Đã duyệt',
      value: statistics?.approvedWithdrawals || 0,
      icon: CheckCircle,
      color: '#10b981',
    },
    {
      title: 'Đã từ chối',
      value: statistics?.rejectedWithdrawals || 0,
      icon: XCircle,
      color: '#ef4444',
    },
    {
      title: 'Tổng tiền rút',
      value: statistics?.totalAmount
        ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
            statistics.totalAmount
          )
        : '0 ₫',
      icon: TrendingUp,
      color: '#8b5cf6',
    },
  ];

  const fetchWithdrawals = async (
    currentPage = page,
    currentSearch = search,
    currentStatus = status,
    currentDateRange = dateRange
  ) => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: PAGE_SIZE.toString(),
        ...(currentSearch && { search: currentSearch }),
        ...(currentStatus && { status: currentStatus }),
        ...(currentDateRange &&
          currentDateRange[0] && { startDate: currentDateRange[0].format('YYYY-MM-DD') }),
        ...(currentDateRange &&
          currentDateRange[1] && { endDate: currentDateRange[1].format('YYYY-MM-DD') }),
      });

      const response = await get<WithdrawalListResponse>(`/management/withdrawals?${queryParams}`);

      setWithdrawals(response.data.data);
      setTotal(response.data.meta.pagination.total);
    } catch (error) {
      console.error('Error fetching withdrawals:', error);
      message.error('Không thể tải danh sách yêu cầu rút tiền');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await get<Statistics>('/management/withdrawals/statistics');
      setStatistics(response.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  // Handle date range change
  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
    setPage(1);
  };

  // Handle withdrawal approval
  const handleApproval = async (
    withdrawalId: number,
    action: 'approve' | 'reject',
    adminNote?: string
  ) => {
    setActionLoading(true);
    try {
      await put(`/management/withdrawals/${withdrawalId}/${action}`, { adminNote });
      message.success(
        action === 'approve' ? 'Đã duyệt yêu cầu rút tiền' : 'Đã từ chối yêu cầu rút tiền'
      );
      fetchWithdrawals();
      fetchStatistics();

      // Close modals
      setRejectModalVisible(false);
      setDetailModalVisible(false);
      adminNoteForm.resetFields();
    } catch (error) {
      console.error('Error updating withdrawal:', error);
      message.error('Không thể cập nhật trạng thái yêu cầu rút tiền');
    } finally {
      setActionLoading(false);
    }
  };

  // Show withdrawal detail modal
  const showWithdrawalDetail = (withdrawal: Withdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setDetailModalVisible(true);
  };

  // Show approval confirmation for approve action
  const showApprovalConfirm = (withdrawal: Withdrawal) => {
    confirm({
      title: 'Duyệt yêu cầu rút tiền',
      content: `Bạn có chắc chắn muốn duyệt yêu cầu rút ${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(withdrawal.amount)} của ${withdrawal.user.name}?`,
      icon: <ExclamationCircleOutlined />,
      okText: 'Duyệt',
      okType: 'primary',
      cancelText: 'Hủy',
      onOk() {
        handleApproval(withdrawal.id, 'approve');
      },
    });
  };

  // Show reject modal with admin note input
  const showRejectModal = (withdrawal: Withdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setRejectModalVisible(true);
    adminNoteForm.resetFields();
  };

  // Handle reject with admin note
  const handleRejectWithNote = async () => {
    try {
      const values = await adminNoteForm.validateFields();
      await handleApproval(selectedWithdrawal!.id, 'reject', values.adminNote);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  // Create and download Excel file
  const createAndDownloadExcel = (withdrawals: Withdrawal[]) => {
    if (!withdrawals.length) {
      message.warning('Không có dữ liệu để xuất');
      return;
    }

    // Prepare Excel data
    const excelData = withdrawals.map((withdrawal, index) => ({
      STT: index + 1,
      ID: withdrawal.id,
      'Đại lý': withdrawal.user.name,
      'Số điện thoại': withdrawal.user.phone,
      Email: withdrawal.user.email,
      'Số tiền rút': new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
        withdrawal.amount
      ),
      'Ngân hàng': withdrawal.bankName,
      'Số tài khoản': withdrawal.bankAccount,
      'Chủ tài khoản': withdrawal.accountHolder,
      'Trạng thái':
        withdrawal.statusPaid === 'pending'
          ? 'Chờ duyệt'
          : withdrawal.statusPaid === 'approved'
            ? 'Đã duyệt'
            : 'Đã từ chối',
      'Ghi chú': withdrawal.note || '',
      'Ghi chú admin': withdrawal.adminNote || '',
      'Ngày tạo': new Date(withdrawal.createdAt).toLocaleString('vi-VN'),
      'Ngày xử lý': withdrawal.processedAt
        ? new Date(withdrawal.processedAt).toLocaleString('vi-VN')
        : '',
    }));

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Set column widths
    const columnWidths = [
      { wch: 5 }, // STT
      { wch: 8 }, // ID
      { wch: 25 }, // Đại lý
      { wch: 15 }, // Số điện thoại
      { wch: 30 }, // Email
      { wch: 20 }, // Số tiền rút
      { wch: 20 }, // Ngân hàng
      { wch: 20 }, // Số tài khoản
      { wch: 25 }, // Chủ tài khoản
      { wch: 15 }, // Trạng thái
      { wch: 30 }, // Ghi chú
      { wch: 30 }, // Ghi chú admin
      { wch: 20 }, // Ngày tạo
      { wch: 20 }, // Ngày xử lý
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Yêu cầu rút tiền');

    // Generate filename with current date
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
    const filename = `yeu-cau-rut-tien-${dateStr}-${timeStr}.xlsx`;

    // Write and download file
    XLSX.writeFile(workbook, filename);
  };

  // Handle export
  const handleExport = async () => {
    try {
      // Prepare query params for export (same as current filters)
      const queryParams = new URLSearchParams({
        page: '1',
        pageSize: '10000', // Get all withdrawals
        ...(search && { search }),
        ...(status && { status }),
        ...(dateRange && dateRange[0] && { startDate: dateRange[0].format('YYYY-MM-DD') }),
        ...(dateRange && dateRange[1] && { endDate: dateRange[1].format('YYYY-MM-DD') }),
      });

      // Fetch all withdrawals with current filters
      const response = await get<WithdrawalListResponse>(`/management/withdrawals?${queryParams}`);
      const withdrawalsToExport = response.data.data || [];

      if (withdrawalsToExport.length === 0) {
        message.warning('Không có yêu cầu rút tiền nào phù hợp với bộ lọc đã chọn');
        return;
      }

      // Create and download Excel file
      createAndDownloadExcel(withdrawalsToExport);
      message.success(
        `Đã tải xuống file Excel với ${withdrawalsToExport.length} yêu cầu rút tiền!`
      );
    } catch (error) {
      console.error('Error exporting withdrawals:', error);
      message.error('Không thể xuất file Excel');
    }
  };

  const columns = [
    {
      title: 'Đại lý',
      key: 'user',
      width: 280,
      render: (record: Withdrawal) => (
        <Space>
          <Avatar
            src={record.user.avatar}
            size={48}
            style={{
              backgroundColor: '#f0f2f5',
              color: '#666',
            }}
          >
            {record.user.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <div>
            <div
              style={{
                fontWeight: 600,
                color: '#1e293b',
                fontSize: '14px',
              }}
            >
              {record.user.name}
            </div>
            <div style={{ fontSize: '12px', color: '#64748b' }}>{record.user.phone}</div>
            <div style={{ fontSize: '12px', color: '#94a3b8' }}>{record.user.email}</div>
          </div>
        </Space>
      ),
      sorter: (a: Withdrawal, b: Withdrawal) => a.user.name.localeCompare(b.user.name),
    },
    {
      title: 'Thông tin rút tiền',
      key: 'withdrawal',
      width: 250,
      render: (record: Withdrawal) => (
        <div>
          <div
            style={{
              fontWeight: 600,
              color: '#059669',
              fontSize: '16px',
              marginBottom: '4px',
            }}
          >
            {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
              record.amount
            )}
          </div>
          <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '2px' }}>
            <BankOutlined style={{ marginRight: 4 }} />
            {record.bankName}
          </div>
          <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '2px' }}>
            {record.bankAccount}
          </div>
          <div style={{ fontSize: '12px', color: '#64748b' }}>{record.accountHolder}</div>
        </div>
      ),
      sorter: (a: Withdrawal, b: Withdrawal) => a.amount - b.amount,
    },
    {
      title: 'Trạng thái',
      key: 'status',
      width: 120,
      render: (record: Withdrawal) => {
        const statusConfig = {
          pending: { color: '#f59e0b', text: 'Chờ duyệt', icon: <ClockCircleOutlined /> },
          approved: { color: '#10b981', text: 'Đã duyệt', icon: <CheckCircleOutlined /> },
          rejected: { color: '#ef4444', text: 'Đã từ chối', icon: <CloseCircleOutlined /> },
        };
        const config = statusConfig[record.statusPaid];

        return (
          <Tag color={config.color} icon={config.icon} style={{ margin: 0, fontWeight: 500 }}>
            {config.text}
          </Tag>
        );
      },
      filters: [
        { text: 'Chờ duyệt', value: 'pending' },
        { text: 'Đã duyệt', value: 'approved' },
        { text: 'Đã từ chối', value: 'rejected' },
      ],
      onFilter: (value: any, record: Withdrawal) => record.statusPaid === value,
    },
    {
      title: 'Thời gian',
      key: 'time',
      width: 180,
      render: (record: Withdrawal) => (
        <div>
          <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '2px' }}>
            <CalendarOutlined style={{ marginRight: 4 }} />
            Tạo: {new Date(record.createdAt).toLocaleString('vi-VN')}
          </div>
          {record.processedAt && (
            <div style={{ fontSize: '12px', color: '#64748b' }}>
              Xử lý: {new Date(record.processedAt).toLocaleString('vi-VN')}
            </div>
          )}
        </div>
      ),
      sorter: (a: Withdrawal, b: Withdrawal) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 250,
      render: (record: Withdrawal) => (
        <Space>
          <Tooltip title="Xem chi tiết">
            <AntButton
              type="text"
              icon={<EyeOutlined />}
              onClick={() => showWithdrawalDetail(record)}
              style={{
                color: '#3b82f6',
                borderRadius: 6,
                width: 32,
                height: 32,
                padding: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            />
          </Tooltip>

          {record.statusPaid === 'pending' && (
            <>
              <Tooltip title="Duyệt yêu cầu">
                <AntButton
                  type="text"
                  icon={<CheckCircleOutlined />}
                  onClick={() => showApprovalConfirm(record)}
                  loading={actionLoading}
                  style={{
                    color: '#10b981',
                    borderRadius: 6,
                    width: 32,
                    height: 32,
                    padding: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                />
              </Tooltip>
              <Tooltip title="Từ chối yêu cầu">
                <AntButton
                  type="text"
                  icon={<CloseCircleOutlined />}
                  onClick={() => showRejectModal(record)}
                  loading={actionLoading}
                  style={{
                    color: '#ef4444',
                    borderRadius: 6,
                    width: 32,
                    height: 32,
                    padding: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                />
              </Tooltip>
            </>
          )}

          {record.statusPaid !== 'pending' && (
            <div style={{ fontSize: '12px', color: '#94a3b8', marginLeft: 8 }}>
              {record.processedBy ? `Bởi: ${record.processedBy.name}` : 'Đã xử lý'}
            </div>
          )}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    fetchWithdrawals();
    fetchStatistics();
  }, [page, search, status, dateRange]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (search !== '') {
        fetchWithdrawals(1, search, status, dateRange);
        setPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  return (
    <PageContainer>
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* Withdrawal Management */}
        <Card style={{ marginBottom: 24 }}>
          <PageHeader
            title="Yêu cầu rút tiền"
            description="Xem và quản lý các yêu cầu rút tiền từ đại lý"
            actions={
              <Space>
                <Button onClick={handleExport} $variant="outline">
                  <Download />
                  Xuất Excel
                </Button>
                <Button
                  onClick={() => {
                    fetchWithdrawals();
                    fetchStatistics();
                  }}
                  $variant="outline"
                >
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </Space>
            }
          />

          <CardContent>
            {/* Status Tabs */}
            <div style={{ marginBottom: 24 }}>
              <Tabs
                activeKey={status || 'all'}
                onChange={(key) => {
                  const newStatus = key === 'all' ? '' : key;
                  setStatus(newStatus);
                  setPage(1);
                }}
                items={[
                  {
                    key: 'all',
                    label: 'Tất cả',
                    children: null,
                  },
                  {
                    key: 'pending',
                    label: (
                      <Badge count={statistics?.pendingWithdrawals || 0} size="small">
                        <span style={{ marginRight: 8 }}>Chờ duyệt</span>
                      </Badge>
                    ),
                    children: null,
                  },
                  {
                    key: 'approved',
                    label: 'Đã duyệt',
                    children: null,
                  },
                  {
                    key: 'rejected',
                    label: 'Đã từ chối',
                    children: null,
                  },
                ]}
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                }}
              />
            </div>

            {/* Filters Section */}
            <FiltersSection>
              <SearchBar
                placeholder="Tìm kiếm theo tên đại lý, số tài khoản..."
                value={search}
                onChange={handleSearch}
              />

              <FilterGroup>
                <FilterLabel>Thời gian:</FilterLabel>
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  format="DD/MM/YYYY"
                  placeholder={['Từ ngày', 'Đến ngày']}
                  style={{ width: 250 }}
                />
              </FilterGroup>
            </FiltersSection>

            {/* Withdrawals Table */}
            <StyledTable>
              <AntTable
                columns={columns}
                dataSource={withdrawals}
                rowKey="id"
                loading={loading}
                pagination={false}
                scroll={{ x: 1200 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span
                          style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}
                        >
                          Không có dữ liệu
                        </span>
                      }
                    />
                  ),
                }}
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                }}
              />
            </StyledTable>

            {/* Pagination */}
            {total > 0 && (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: 16,
                  padding: '16px 0',
                  borderTop: '1px solid #f0f0f0',
                }}
              >
                <span style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}>
                  Hiển thị {(page - 1) * PAGE_SIZE + 1} - {Math.min(page * PAGE_SIZE, total)} của{' '}
                  {total} yêu cầu
                </span>
                <Pagination
                  current={page}
                  pageSize={PAGE_SIZE}
                  total={total}
                  onChange={(newPage) => {
                    setPage(newPage);
                    fetchWithdrawals(newPage, search, status, dateRange);
                  }}
                  showSizeChanger={false}
                  style={{
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Withdrawal Detail Modal */}
        <Modal
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <InfoCircleOutlined style={{ color: '#3b82f6' }} />
              Chi tiết yêu cầu rút tiền
            </div>
          }
          open={detailModalVisible}
          onCancel={() => setDetailModalVisible(false)}
          footer={
            selectedWithdrawal?.statusPaid === 'pending'
              ? [
                  <AntButton key="cancel" onClick={() => setDetailModalVisible(false)}>
                    Đóng
                  </AntButton>,
                  <AntButton
                    key="reject"
                    danger
                    icon={<CloseCircleOutlined />}
                    onClick={() => {
                      setDetailModalVisible(false);
                      showRejectModal(selectedWithdrawal);
                    }}
                    loading={actionLoading}
                  >
                    Từ chối
                  </AntButton>,
                  <AntButton
                    key="approve"
                    type="primary"
                    icon={<CheckCircleOutlined />}
                    onClick={() => showApprovalConfirm(selectedWithdrawal)}
                    loading={actionLoading}
                  >
                    Duyệt
                  </AntButton>,
                ]
              : [
                  <AntButton
                    key="close"
                    type="primary"
                    onClick={() => setDetailModalVisible(false)}
                  >
                    Đóng
                  </AntButton>,
                ]
          }
          width={800}
          style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}
        >
          {selectedWithdrawal && (
            <div style={{ padding: '16px 0' }}>
              <Descriptions
                bordered
                column={2}
                size="small"
                labelStyle={{
                  backgroundColor: '#f8fafc',
                  fontWeight: 600,
                  color: '#374151',
                }}
                contentStyle={{
                  backgroundColor: '#ffffff',
                  color: '#1f2937',
                }}
              >
                <Descriptions.Item label="ID" span={1}>
                  #{selectedWithdrawal.id}
                </Descriptions.Item>
                <Descriptions.Item label="Trạng thái" span={1}>
                  <Tag
                    color={
                      selectedWithdrawal.statusPaid === 'pending'
                        ? '#f59e0b'
                        : selectedWithdrawal.statusPaid === 'approved'
                          ? '#10b981'
                          : '#ef4444'
                    }
                    style={{ margin: 0, fontWeight: 500 }}
                  >
                    {selectedWithdrawal.statusPaid === 'pending'
                      ? 'Chờ duyệt'
                      : selectedWithdrawal.statusPaid === 'approved'
                        ? 'Đã duyệt'
                        : 'Đã từ chối'}
                  </Tag>
                </Descriptions.Item>

                <Descriptions.Item label="Tên đại lý" span={1}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <Avatar src={selectedWithdrawal.user.avatar} size={24}>
                      {selectedWithdrawal.user.name?.charAt(0)?.toUpperCase()}
                    </Avatar>
                    {selectedWithdrawal.user.name}
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label="Số điện thoại" span={1}>
                  {selectedWithdrawal.user.phone}
                </Descriptions.Item>

                <Descriptions.Item label="Email" span={2}>
                  {selectedWithdrawal.user.email}
                </Descriptions.Item>

                <Descriptions.Item label="Số tiền rút" span={1}>
                  <span style={{ fontWeight: 600, color: '#059669', fontSize: '16px' }}>
                    {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
                      selectedWithdrawal.amount
                    )}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="Số dư hiện tại" span={1}>
                  <span style={{ fontWeight: 600, color: '#3b82f6' }}>
                    {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
                      selectedWithdrawal.user.balance
                    )}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="Ngân hàng" span={1}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                    <BankOutlined />
                    {selectedWithdrawal.bankName}
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label="Số tài khoản" span={1}>
                  {selectedWithdrawal.bankAccount}
                </Descriptions.Item>

                <Descriptions.Item label="Chủ tài khoản" span={2}>
                  {selectedWithdrawal.accountHolder}
                </Descriptions.Item>

                {selectedWithdrawal.note && (
                  <Descriptions.Item label="Ghi chú" span={2}>
                    {selectedWithdrawal.note}
                  </Descriptions.Item>
                )}

                {selectedWithdrawal.adminNote && (
                  <Descriptions.Item label="Ghi chú admin" span={2}>
                    <span style={{ color: '#dc2626' }}>{selectedWithdrawal.adminNote}</span>
                  </Descriptions.Item>
                )}

                <Descriptions.Item label="Ngày tạo" span={1}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                    <CalendarOutlined />
                    {new Date(selectedWithdrawal.createdAt).toLocaleString('vi-VN')}
                  </div>
                </Descriptions.Item>

                {selectedWithdrawal.processedAt && (
                  <Descriptions.Item label="Ngày xử lý" span={1}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      <ClockCircleOutlined />
                      {new Date(selectedWithdrawal.processedAt).toLocaleString('vi-VN')}
                    </div>
                  </Descriptions.Item>
                )}

                {selectedWithdrawal.processedBy && (
                  <Descriptions.Item label="Người xử lý" span={1}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      <UserOutlined />
                      {selectedWithdrawal.processedBy.name}
                    </div>
                  </Descriptions.Item>
                )}
              </Descriptions>
            </div>
          )}
        </Modal>

        {/* Reject Modal with Admin Note */}
        <Modal
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <CloseCircleOutlined style={{ color: '#ef4444' }} />
              Từ chối yêu cầu rút tiền
            </div>
          }
          open={rejectModalVisible}
          onCancel={() => {
            setRejectModalVisible(false);
            adminNoteForm.resetFields();
          }}
          footer={[
            <AntButton
              key="cancel"
              onClick={() => {
                setRejectModalVisible(false);
                adminNoteForm.resetFields();
              }}
            >
              Hủy
            </AntButton>,
            <AntButton
              key="reject"
              danger
              type="primary"
              icon={<CloseCircleOutlined />}
              onClick={handleRejectWithNote}
              loading={actionLoading}
            >
              Từ chối
            </AntButton>,
          ]}
          width={600}
          style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}
        >
          {selectedWithdrawal && (
            <div>
              <div
                style={{
                  marginBottom: 16,
                  padding: 16,
                  backgroundColor: '#fef2f2',
                  borderRadius: 8,
                  border: '1px solid #fecaca',
                }}
              >
                <div style={{ fontWeight: 600, marginBottom: 8 }}>Thông tin yêu cầu:</div>
                <div>
                  Đại lý: <strong>{selectedWithdrawal.user.name}</strong>
                </div>
                <div>
                  Số tiền:{' '}
                  <strong style={{ color: '#dc2626' }}>
                    {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
                      selectedWithdrawal.amount
                    )}
                  </strong>
                </div>
                <div>
                  Ngân hàng:{' '}
                  <strong>
                    {selectedWithdrawal.bankName} - {selectedWithdrawal.bankAccount}
                  </strong>
                </div>
              </div>

              <Form form={adminNoteForm} layout="vertical">
                <Form.Item
                  name="adminNote"
                  label="Lý do từ chối"
                  rules={[
                    { required: true, message: 'Vui lòng nhập lý do từ chối' },
                    { min: 10, message: 'Lý do từ chối phải có ít nhất 10 ký tự' },
                  ]}
                >
                  <Input.TextArea
                    rows={4}
                    placeholder="Nhập lý do từ chối yêu cầu rút tiền..."
                    style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}
                  />
                </Form.Item>
              </Form>
            </div>
          )}
        </Modal>
      </Spin>
    </PageContainer>
  );
};

export default WithdrawalList;
