import React from 'react';
import {
  StatCard,
  StatContent,
  StatInfo,
  StatTitle,
  StatValue,
  StatIcon,
} from './StyledComponents';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: 'bg-blue' | 'bg-blue-dark' | 'bg-green' | 'bg-red' | string;
  subtitle?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon, color, subtitle }) => {
  return (
    <StatCard>
      <StatContent>
        <StatInfo>
          <StatTitle>{title}</StatTitle>
          <StatValue>{value}</StatValue>
          {subtitle && (
            <div
              style={{
                fontSize: '12px',
                color: '#64748b',
                marginTop: '4px',
                fontFamily: "'Be Vietnam Pro', sans-serif",
              }}
            >
              {subtitle}
            </div>
          )}
        </StatInfo>
        <StatIcon $color={color}>{icon}</StatIcon>
      </StatContent>
    </StatCard>
  );
};

export default StatsCard;
