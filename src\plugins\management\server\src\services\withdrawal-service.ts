import type { Core } from '@strapi/strapi';

const withdrawalService = ({ strapi }: { strapi: Core.Strapi }) => ({
  async listWithdrawals(query: any) {
    const { page = 1, pageSize = 10, search, status, startDate, endDate } = query;

    // Build filters
    const filters: any = {};

    if (status) {
      filters.statusPaid = { $eq: status };
    }

    if (search) {
      filters.$or = [
        { user: { name: { $containsi: search } } },
        { user: { phone: { $containsi: search } } },
        { user: { email: { $containsi: search } } },
        { bankAccount: { $containsi: search } },
        { bankName: { $containsi: search } },
        { accountHolder: { $containsi: search } },
      ];
    }

    if (startDate || endDate) {
      filters.createdAt = {};
      if (startDate) {
        filters.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        filters.createdAt.$lte = new Date(endDate + 'T23:59:59.999Z');
      }
    }

    try {
      const { results, pagination } = await strapi.entityService.findPage(
        'api::rut-tien.rut-tien',
        {
          filters,
          populate: {
            user: {
              populate: {
                image: true,
              },
            },
            processedBy: true,
          },
          sort: { createdAt: 'desc' },
          page: parseInt(page),
          pageSize: parseInt(pageSize),
        }
      );

      // Transform data to match frontend expectations
      const transformedData = results.map((withdrawal: any) => ({
        id: withdrawal.id,
        amount: parseFloat(withdrawal.amount),
        bankName: withdrawal.bankName,
        bankAccount: withdrawal.bankAccount,
        accountHolder: withdrawal.accountHolder,
        status: withdrawal.statusPaid,
        note: withdrawal.note,
        adminNote: withdrawal.adminNote,
        user: {
          id: withdrawal.user.id,
          name: withdrawal.user.name,
          phone: withdrawal.user.phone,
          email: withdrawal.user.email,
          avatar: withdrawal.user.image?.url,
          balance: parseFloat(withdrawal.user.balance || 0),
        },
        createdAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt,
        processedAt: withdrawal.processedAt,
        processedBy: withdrawal.processedBy
          ? {
              id: withdrawal.processedBy.id,
              name: `${withdrawal.processedBy.firstname} ${withdrawal.processedBy.lastname}`.trim(),
            }
          : null,
      }));

      return {
        data: transformedData,
        meta: { pagination },
      };
    } catch (error) {
      console.error('Error fetching withdrawals:', error);
      throw error;
    }
  },

  async getWithdrawalDetail(id: number) {
    try {
      const withdrawal = await strapi.entityService.findOne('api::rut-tien.rut-tien', id, {
        populate: {
          user: {
            populate: {
              image: true,
            },
          },
          processedBy: true,
        },
      });

      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      return {
        id: withdrawal.id,
        amount: parseFloat(withdrawal.amount),
        bankName: withdrawal.bankName,
        bankAccount: withdrawal.bankAccount,
        accountHolder: withdrawal.accountHolder,
        status: withdrawal.statusPaid,
        note: withdrawal.note,
        adminNote: withdrawal.adminNote,
        user: {
          id: withdrawal.user.id,
          name: withdrawal.user.name,
          phone: withdrawal.user.phone,
          email: withdrawal.user.email,
          avatar: withdrawal.user.image?.url,
          balance: parseFloat(withdrawal.user.balance || 0),
        },
        createdAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt,
        processedAt: withdrawal.processedAt,
        processedBy: withdrawal.processedBy
          ? {
              id: withdrawal.processedBy.id,
              name: `${withdrawal.processedBy.firstname} ${withdrawal.processedBy.lastname}`.trim(),
            }
          : null,
      };
    } catch (error) {
      console.error('Error fetching withdrawal detail:', error);
      throw error;
    }
  },

  async approveWithdrawal(id: number, adminUser: any) {
    try {
      const withdrawal = await strapi.entityService.findOne('api::rut-tien.rut-tien', id, {
        populate: { user: true },
      });

      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      if (withdrawal.statusPaid !== 'pending') {
        throw new Error('Withdrawal is not pending');
      }

      // Update withdrawal status
      const updatedWithdrawal = await strapi.entityService.update('api::rut-tien.rut-tien', id, {
        data: {
          statusPaid: 'approved',
          processedAt: new Date(),
          processedBy: adminUser.id,
        } as any,
      });

      return updatedWithdrawal;
    } catch (error) {
      console.error('Error approving withdrawal:', error);
      throw error;
    }
  },

  async rejectWithdrawal(id: number, adminNote: string, adminUser: any) {
    try {
      const withdrawal = await strapi.entityService.findOne('api::rut-tien.rut-tien', id, {
        populate: { user: true },
      });

      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      if (withdrawal.statusPaid !== 'pending') {
        throw new Error('Withdrawal is not pending');
      }

      // Update withdrawal status and refund balance
      const updatedWithdrawal = await strapi.entityService.update('api::rut-tien.rut-tien', id, {
        data: {
          statusPaid: 'rejected',
          adminNote: adminNote,
          processedAt: new Date(),
          processedBy: adminUser.id,
        } as any,
      });

      // Refund the amount to user balance
      const currentBalance = parseFloat(withdrawal.user.balance || 0);
      const refundAmount = parseFloat(withdrawal.amount);

      await strapi.entityService.update('plugin::users-permissions.user', withdrawal.user.id, {
        data: {
          balance: currentBalance + refundAmount,
        } as any,
      });

      return updatedWithdrawal;
    } catch (error) {
      console.error('Error rejecting withdrawal:', error);
      throw error;
    }
  },

  async getWithdrawalStatistics(query: any) {
    try {
      const { startDate, endDate, period = 'all' } = query;

      // Build date filter based on period or custom date range
      let dateFilter: any = {};
      const now = new Date();

      if (startDate && endDate) {
        // Custom date range
        dateFilter = {
          $gte: new Date(startDate),
          $lte: new Date(endDate + 'T23:59:59.999Z'),
        };
      } else {
        // Predefined periods
        switch (period) {
          case 'today':
            const startOfDay = new Date(now);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(now);
            endOfDay.setHours(23, 59, 59, 999);
            dateFilter = { $gte: startOfDay, $lte: endOfDay };
            break;
          case 'week':
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - 7);
            startOfWeek.setHours(0, 0, 0, 0);
            dateFilter = { $gte: startOfWeek };
            break;
          case 'month':
            const startOfMonth = new Date(now);
            startOfMonth.setDate(now.getDate() - 30);
            startOfMonth.setHours(0, 0, 0, 0);
            dateFilter = { $gte: startOfMonth };
            break;
          case 'year':
            const startOfYear = new Date(now);
            startOfYear.setFullYear(now.getFullYear() - 1);
            startOfYear.setHours(0, 0, 0, 0);
            dateFilter = { $gte: startOfYear };
            break;
          default:
            // 'all' - no date filter
            dateFilter = {};
        }
      }

      // Get all withdrawals with date filter
      const filters = Object.keys(dateFilter).length ? { createdAt: dateFilter } : {};
      const allWithdrawals = await strapi.entityService.findMany('api::rut-tien.rut-tien', {
        filters,
        populate: {
          user: {
            fields: ['id', 'name'],
          },
          processedBy: {
            fields: ['id', 'firstname', 'lastname'],
          },
        },
      });

      // Calculate basic statistics
      const basicStats = allWithdrawals.reduce(
        (acc: any, withdrawal: any) => {
          const amount = parseFloat(withdrawal.amount) || 0;

          acc.totalWithdrawals++;
          acc.totalAmount += amount;

          switch (withdrawal.statusPaid) {
            case 'pending':
              acc.pendingWithdrawals++;
              acc.pendingAmount += amount;
              break;
            case 'approved':
              acc.approvedWithdrawals++;
              acc.approvedAmount += amount;
              break;
            case 'rejected':
              acc.rejectedWithdrawals++;
              acc.rejectedAmount += amount;
              break;
          }

          return acc;
        },
        {
          totalWithdrawals: 0,
          pendingWithdrawals: 0,
          approvedWithdrawals: 0,
          rejectedWithdrawals: 0,
          totalAmount: 0,
          pendingAmount: 0,
          approvedAmount: 0,
          rejectedAmount: 0,
        }
      );

      // Calculate additional statistics
      const additionalStats = {
        // Average withdrawal amount
        averageWithdrawalAmount:
          basicStats.totalWithdrawals > 0
            ? basicStats.totalAmount / basicStats.totalWithdrawals
            : 0,

        // Average approved withdrawal amount
        averageApprovedAmount:
          basicStats.approvedWithdrawals > 0
            ? basicStats.approvedAmount / basicStats.approvedWithdrawals
            : 0,

        // Approval rate (percentage)
        approvalRate:
          basicStats.totalWithdrawals > 0
            ? (basicStats.approvedWithdrawals / basicStats.totalWithdrawals) * 100
            : 0,

        // Rejection rate (percentage)
        rejectionRate:
          basicStats.totalWithdrawals > 0
            ? (basicStats.rejectedWithdrawals / basicStats.totalWithdrawals) * 100
            : 0,

        // Pending rate (percentage)
        pendingRate:
          basicStats.totalWithdrawals > 0
            ? (basicStats.pendingWithdrawals / basicStats.totalWithdrawals) * 100
            : 0,
      };

      // Get top users by withdrawal amount (for the filtered period)
      const userWithdrawals = allWithdrawals.reduce((acc: any, withdrawal: any) => {
        if (withdrawal.user) {
          const userId = withdrawal.user.id;
          if (!acc[userId]) {
            acc[userId] = {
              userId,
              userName: withdrawal.user.name,
              totalAmount: 0,
              totalWithdrawals: 0,
              approvedAmount: 0,
              approvedWithdrawals: 0,
            };
          }

          const amount = parseFloat(withdrawal.amount) || 0;
          acc[userId].totalAmount += amount;
          acc[userId].totalWithdrawals++;

          if (withdrawal.statusPaid === 'approved') {
            acc[userId].approvedAmount += amount;
            acc[userId].approvedWithdrawals++;
          }
        }
        return acc;
      }, {});

      const topUsers = Object.values(userWithdrawals)
        .sort((a: any, b: any) => b.totalAmount - a.totalAmount)
        .slice(0, 5);

      // Get daily statistics for the last 30 days (for trend analysis)
      const thirtyDaysAgo = new Date(now);
      thirtyDaysAgo.setDate(now.getDate() - 30);
      thirtyDaysAgo.setHours(0, 0, 0, 0);

      const recentWithdrawals = await strapi.entityService.findMany('api::rut-tien.rut-tien', {
        filters: {
          createdAt: { $gte: thirtyDaysAgo },
        },
        fields: ['amount', 'statusPaid', 'createdAt'],
      });

      // Group by day for trend data
      const dailyStats = recentWithdrawals.reduce((acc: any, withdrawal: any) => {
        const date = new Date(withdrawal.createdAt).toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = {
            date,
            totalAmount: 0,
            totalCount: 0,
            approvedAmount: 0,
            approvedCount: 0,
            pendingAmount: 0,
            pendingCount: 0,
            rejectedAmount: 0,
            rejectedCount: 0,
          };
        }

        const amount = parseFloat(withdrawal.amount) || 0;
        acc[date].totalAmount += amount;
        acc[date].totalCount++;

        switch (withdrawal.statusPaid) {
          case 'approved':
            acc[date].approvedAmount += amount;
            acc[date].approvedCount++;
            break;
          case 'pending':
            acc[date].pendingAmount += amount;
            acc[date].pendingCount++;
            break;
          case 'rejected':
            acc[date].rejectedAmount += amount;
            acc[date].rejectedCount++;
            break;
        }

        return acc;
      }, {});

      const trendData = Object.values(dailyStats).sort(
        (a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      return {
        ...basicStats,
        ...additionalStats,
        topUsers,
        trendData,
        period,
        dateRange: Object.keys(dateFilter).length
          ? {
              startDate: dateFilter.$gte?.toISOString().split('T')[0],
              endDate: dateFilter.$lte?.toISOString().split('T')[0],
            }
          : null,
      };
    } catch (error) {
      console.error('Error fetching withdrawal statistics:', error);
      throw error;
    }
  },
});

export default withdrawalService;
