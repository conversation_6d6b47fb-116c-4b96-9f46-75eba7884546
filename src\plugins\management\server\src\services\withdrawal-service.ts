import type { Core } from '@strapi/strapi';

const withdrawalService = ({ strapi }: { strapi: Core.Strapi }) => ({
  async listWithdrawals(query: any) {
    const { page = 1, pageSize = 10, search, status, startDate, endDate } = query;

    // Build filters
    const filters: any = {};

    if (status) {
      filters.statusPaid = { $eq: status };
    }

    if (search) {
      filters.$or = [
        { user: { name: { $containsi: search } } },
        { user: { phone: { $containsi: search } } },
        { user: { email: { $containsi: search } } },
        { bankAccount: { $containsi: search } },
        { bankName: { $containsi: search } },
        { accountHolder: { $containsi: search } },
      ];
    }

    if (startDate || endDate) {
      filters.createdAt = {};
      if (startDate) {
        filters.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        filters.createdAt.$lte = new Date(endDate + 'T23:59:59.999Z');
      }
    }

    try {
      const { results, pagination } = await strapi.entityService.findPage(
        'api::rut-tien.rut-tien',
        {
          filters,
          populate: {
            user: {
              populate: {
                image: true,
              },
            },
            processedBy: true,
          },
          sort: { createdAt: 'desc' },
          page: parseInt(page),
          pageSize: parseInt(pageSize),
        }
      );

      // Transform data to match frontend expectations
      const transformedData = results.map((withdrawal: any) => ({
        id: withdrawal.id,
        amount: parseFloat(withdrawal.amount),
        bankName: withdrawal.bankName,
        bankAccount: withdrawal.bankAccount,
        accountHolder: withdrawal.accountHolder,
        status: withdrawal.statusPaid,
        note: withdrawal.note,
        adminNote: withdrawal.adminNote,
        user: {
          id: withdrawal.user.id,
          name: withdrawal.user.name,
          phone: withdrawal.user.phone,
          email: withdrawal.user.email,
          avatar: withdrawal.user.image?.url,
          balance: parseFloat(withdrawal.user.balance || 0),
        },
        createdAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt,
        processedAt: withdrawal.processedAt,
        processedBy: withdrawal.processedBy
          ? {
              id: withdrawal.processedBy.id,
              name: `${withdrawal.processedBy.firstname} ${withdrawal.processedBy.lastname}`.trim(),
            }
          : null,
      }));

      return {
        data: transformedData,
        meta: { pagination },
      };
    } catch (error) {
      console.error('Error fetching withdrawals:', error);
      throw error;
    }
  },

  async getWithdrawalDetail(id: number) {
    try {
      const withdrawal = await strapi.entityService.findOne('api::rut-tien.rut-tien', id, {
        populate: {
          user: {
            populate: {
              image: true,
            },
          },
          processedBy: true,
        },
      });

      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      return {
        id: withdrawal.id,
        amount: parseFloat(withdrawal.amount),
        bankName: withdrawal.bankName,
        bankAccount: withdrawal.bankAccount,
        accountHolder: withdrawal.accountHolder,
        status: withdrawal.statusPaid,
        note: withdrawal.note,
        adminNote: withdrawal.adminNote,
        user: {
          id: withdrawal.user.id,
          name: withdrawal.user.name,
          phone: withdrawal.user.phone,
          email: withdrawal.user.email,
          avatar: withdrawal.user.image?.url,
          balance: parseFloat(withdrawal.user.balance || 0),
        },
        createdAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt,
        processedAt: withdrawal.processedAt,
        processedBy: withdrawal.processedBy
          ? {
              id: withdrawal.processedBy.id,
              name: `${withdrawal.processedBy.firstname} ${withdrawal.processedBy.lastname}`.trim(),
            }
          : null,
      };
    } catch (error) {
      console.error('Error fetching withdrawal detail:', error);
      throw error;
    }
  },

  async approveWithdrawal(id: number, adminUser: any) {
    try {
      const withdrawal = await strapi.entityService.findOne('api::rut-tien.rut-tien', id, {
        populate: { user: true },
      });

      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      if (withdrawal.statusPaid !== 'pending') {
        throw new Error('Withdrawal is not pending');
      }

      // Update withdrawal status
      const updatedWithdrawal = await strapi.entityService.update('api::rut-tien.rut-tien', id, {
        data: {
          statusPaid: 'approved',
          processedAt: new Date(),
          processedBy: adminUser.id,
        } as any,
      });

      return updatedWithdrawal;
    } catch (error) {
      console.error('Error approving withdrawal:', error);
      throw error;
    }
  },

  async rejectWithdrawal(id: number, adminNote: string, adminUser: any) {
    try {
      const withdrawal = await strapi.entityService.findOne('api::rut-tien.rut-tien', id, {
        populate: { user: true },
      });

      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      if (withdrawal.statusPaid !== 'pending') {
        throw new Error('Withdrawal is not pending');
      }

      // Update withdrawal status and refund balance
      const updatedWithdrawal = await strapi.entityService.update('api::rut-tien.rut-tien', id, {
        data: {
          statusPaid: 'rejected',
          adminNote: adminNote,
          processedAt: new Date(),
          processedBy: adminUser.id,
        } as any,
      });

      // Refund the amount to user balance
      const currentBalance = parseFloat(withdrawal.user.balance || 0);
      const refundAmount = parseFloat(withdrawal.amount);

      await strapi.entityService.update('plugin::users-permissions.user', withdrawal.user.id, {
        data: {
          balance: currentBalance + refundAmount,
        } as any,
      });

      return updatedWithdrawal;
    } catch (error) {
      console.error('Error rejecting withdrawal:', error);
      throw error;
    }
  },

  async getWithdrawalStatistics(query: any) {
    try {
      const { startDate, endDate, period = 'all' } = query;

      // Build date filter based on period or custom date range
      let dateFilter: any = {};
      const now = new Date();

      if (startDate && endDate) {
        // Custom date range
        dateFilter = {
          $gte: new Date(startDate),
          $lte: new Date(endDate + 'T23:59:59.999Z'),
        };
      } else {
        // Predefined periods
        switch (period) {
          case 'today':
            const startOfDay = new Date(now);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(now);
            endOfDay.setHours(23, 59, 59, 999);
            dateFilter = { $gte: startOfDay, $lte: endOfDay };
            break;
          case 'week':
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - 7);
            startOfWeek.setHours(0, 0, 0, 0);
            dateFilter = { $gte: startOfWeek };
            break;
          case 'month':
            const startOfMonth = new Date(now);
            startOfMonth.setDate(now.getDate() - 30);
            startOfMonth.setHours(0, 0, 0, 0);
            dateFilter = { $gte: startOfMonth };
            break;
          case 'year':
            const startOfYear = new Date(now);
            startOfYear.setFullYear(now.getFullYear() - 1);
            startOfYear.setHours(0, 0, 0, 0);
            dateFilter = { $gte: startOfYear };
            break;
          default:
            // 'all' - no date filter
            dateFilter = {};
        }
      }

      // Get all withdrawals with date filter
      const filters = Object.keys(dateFilter).length ? { createdAt: dateFilter } : {};
      const allWithdrawals = await strapi.entityService.findMany('api::rut-tien.rut-tien', {
        filters,
        populate: {
          user: {
            fields: ['id', 'name'],
          },
          processedBy: {
            fields: ['id', 'firstname', 'lastname'],
          },
        },
      });

      // Calculate basic statistics
      const basicStats = allWithdrawals.reduce(
        (acc: any, withdrawal: any) => {
          const amount = parseFloat(withdrawal.amount) || 0;

          acc.totalWithdrawals++;
          acc.totalAmount += amount;

          switch (withdrawal.statusPaid) {
            case 'pending':
              acc.pendingWithdrawals++;
              acc.pendingAmount += amount;
              break;
            case 'approved':
              acc.approvedWithdrawals++;
              acc.approvedAmount += amount;
              break;
            case 'rejected':
              acc.rejectedWithdrawals++;
              acc.rejectedAmount += amount;
              break;
          }

          return acc;
        },
        {
          totalWithdrawals: 0,
          pendingWithdrawals: 0,
          approvedWithdrawals: 0,
          rejectedWithdrawals: 0,
          totalAmount: 0,
          pendingAmount: 0,
          approvedAmount: 0,
          rejectedAmount: 0,
        }
      );

      // Calculate additional statistics
      const additionalStats = {
        // Largest withdrawal amount
        largestWithdrawalAmount:
          allWithdrawals.length > 0
            ? Math.max(...allWithdrawals.map((w) => parseFloat(w.amount) || 0))
            : 0,

        // Smallest withdrawal amount
        smallestWithdrawalAmount:
          allWithdrawals.length > 0
            ? Math.min(...allWithdrawals.map((w) => parseFloat(w.amount) || 0))
            : 0,

        // Average processing time (in hours) for approved withdrawals
        averageProcessingTime: (() => {
          const approvedWithProcessTime = allWithdrawals.filter(
            (w) => w.statusPaid === 'approved' && w.processedAt && w.createdAt
          );
          if (approvedWithProcessTime.length === 0) return 0;

          const totalHours = approvedWithProcessTime.reduce((sum, w) => {
            const created = new Date(w.createdAt).getTime();
            const processed = new Date(w.processedAt).getTime();
            return sum + (processed - created) / (1000 * 60 * 60); // Convert to hours
          }, 0);

          return totalHours / approvedWithProcessTime.length;
        })(),

        // Most active day of week for withdrawals
        mostActiveDay: (() => {
          const dayStats = allWithdrawals.reduce((acc, w) => {
            const dayOfWeek = new Date(w.createdAt).getDay();
            const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
            const dayName = dayNames[dayOfWeek];
            acc[dayName] = (acc[dayName] || 0) + 1;
            return acc;
          }, {});

          return Object.keys(dayStats).length > 0
            ? Object.entries(dayStats).reduce((a, b) =>
                dayStats[a[0]] > dayStats[b[0]] ? a : b
              )[0]
            : 'Không có dữ liệu';
        })(),

        // Peak hour for withdrawals (0-23)
        peakHour: (() => {
          const hourStats = allWithdrawals.reduce((acc, w) => {
            const hour = new Date(w.createdAt).getHours();
            acc[hour] = (acc[hour] || 0) + 1;
            return acc;
          }, {});

          if (Object.keys(hourStats).length === 0) return 'Không có dữ liệu';

          const peakHourNum = Object.entries(hourStats).reduce((a, b) =>
            hourStats[a[0]] > hourStats[b[0]] ? a : b
          )[0];

          return `${peakHourNum}:00`;
        })(),

        // Total unique users who made withdrawals
        totalUniqueUsers: new Set(allWithdrawals.map((w) => w.user?.id).filter(Boolean)).size,

        // Average withdrawals per user
        averageWithdrawalsPerUser: (() => {
          const uniqueUsers = new Set(allWithdrawals.map((w) => w.user?.id).filter(Boolean)).size;
          return uniqueUsers > 0 ? (basicStats.totalWithdrawals / uniqueUsers).toFixed(1) : 0;
        })(),
      };

      // Get bank statistics
      const bankStats = allWithdrawals.reduce((acc: any, withdrawal: any) => {
        const bankName = withdrawal.bankName || 'Không xác định';
        if (!acc[bankName]) {
          acc[bankName] = {
            bankName,
            totalAmount: 0,
            totalWithdrawals: 0,
            approvedAmount: 0,
            approvedWithdrawals: 0,
          };
        }

        const amount = parseFloat(withdrawal.amount) || 0;
        acc[bankName].totalAmount += amount;
        acc[bankName].totalWithdrawals++;

        if (withdrawal.statusPaid === 'approved') {
          acc[bankName].approvedAmount += amount;
          acc[bankName].approvedWithdrawals++;
        }
        return acc;
      }, {});

      const topBanks = Object.values(bankStats)
        .sort((a: any, b: any) => b.totalAmount - a.totalAmount)
        .slice(0, 5);

      // Get monthly trend for the last 6 months
      const sixMonthsAgo = new Date(now);
      sixMonthsAgo.setMonth(now.getMonth() - 6);

      const monthlyWithdrawals = await strapi.entityService.findMany('api::rut-tien.rut-tien', {
        filters: {
          createdAt: { $gte: sixMonthsAgo },
        },
        fields: ['amount', 'statusPaid', 'createdAt'],
      });

      const monthlyStats = monthlyWithdrawals.reduce((acc: any, withdrawal: any) => {
        const monthKey = new Date(withdrawal.createdAt).toISOString().slice(0, 7); // YYYY-MM
        if (!acc[monthKey]) {
          acc[monthKey] = {
            month: monthKey,
            totalAmount: 0,
            totalCount: 0,
            approvedAmount: 0,
            approvedCount: 0,
          };
        }

        const amount = parseFloat(withdrawal.amount) || 0;
        acc[monthKey].totalAmount += amount;
        acc[monthKey].totalCount++;

        if (withdrawal.statusPaid === 'approved') {
          acc[monthKey].approvedAmount += amount;
          acc[monthKey].approvedCount++;
        }

        return acc;
      }, {});

      const monthlyTrend = Object.values(monthlyStats).sort(
        (a: any, b: any) => new Date(a.month).getTime() - new Date(b.month).getTime()
      );

      // Get daily statistics for the last 30 days (for trend analysis)
      const thirtyDaysAgo = new Date(now);
      thirtyDaysAgo.setDate(now.getDate() - 30);
      thirtyDaysAgo.setHours(0, 0, 0, 0);

      const recentWithdrawals = await strapi.entityService.findMany('api::rut-tien.rut-tien', {
        filters: {
          createdAt: { $gte: thirtyDaysAgo },
        },
        fields: ['amount', 'statusPaid', 'createdAt'],
      });

      // Group by day for trend data
      const dailyStats = recentWithdrawals.reduce((acc: any, withdrawal: any) => {
        const date = new Date(withdrawal.createdAt).toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = {
            date,
            totalAmount: 0,
            totalCount: 0,
            approvedAmount: 0,
            approvedCount: 0,
            pendingAmount: 0,
            pendingCount: 0,
            rejectedAmount: 0,
            rejectedCount: 0,
          };
        }

        const amount = parseFloat(withdrawal.amount) || 0;
        acc[date].totalAmount += amount;
        acc[date].totalCount++;

        switch (withdrawal.statusPaid) {
          case 'approved':
            acc[date].approvedAmount += amount;
            acc[date].approvedCount++;
            break;
          case 'pending':
            acc[date].pendingAmount += amount;
            acc[date].pendingCount++;
            break;
          case 'rejected':
            acc[date].rejectedAmount += amount;
            acc[date].rejectedCount++;
            break;
        }

        return acc;
      }, {});

      const trendData = Object.values(dailyStats).sort(
        (a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      return {
        ...basicStats,
        ...additionalStats,
        topBanks,
        monthlyTrend,
        trendData,
        period,
        dateRange: Object.keys(dateFilter).length
          ? {
              startDate: dateFilter.$gte?.toISOString().split('T')[0],
              endDate: dateFilter.$lte?.toISOString().split('T')[0],
            }
          : null,
      };
    } catch (error) {
      console.error('Error fetching withdrawal statistics:', error);
      throw error;
    }
  },
});

export default withdrawalService;
